[project]
name = "test"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "torch==2.6.0",
    "torchvision==0.21.0",
    "torchaudio==2.6.0",
    "hslcommunication>=1.2.0",
]

[tool.uv.sources]
torch = [
  { index = "pytorch-cu126", marker = "platform_system == 'Windows'" },
]
torchvision = [
  { index = "pytorch-cu126", marker = "platform_system == 'Windows'" },
]
torchaudio = [
  { index = "pytorch-cu126", marker = "platform_system == 'Windows'" },
]

[[tool.uv.index]]
name = "pytorch-cu126"
url = "https://download.pytorch.org/whl/cu126"
explicit = true
