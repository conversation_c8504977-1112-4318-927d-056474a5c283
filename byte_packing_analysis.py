#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
位元組打包方式差異分析
"""

import struct

def analyze_byte_packing_differences():
    """分析兩種位元組打包方式的差異"""
    print("🔍 位元組打包方式差異分析")
    print("=" * 50)
    
    # 測試數據
    test_values = [4, 10, 100, 1000, 65535]
    
    print("📊 兩種方法對比:")
    print("商業版方法: struct.pack('<H', len(value)//2)")
    print("LGPL版方法: len(value) // 2 % 256")
    print()
    
    for value_len in test_values:
        print(f"數據長度: {value_len} 字節")
        
        # 商業版方法 (使用 struct.pack)
        try:
            commercial_packed = struct.pack('<H', value_len//2)
            commercial_low = commercial_packed[0]
            commercial_high = commercial_packed[1]
            commercial_result = f"Low: {commercial_low}, High: {commercial_high}"
        except Exception as e:
            commercial_result = f"錯誤: {e}"
        
        # LGPL版方法 (簡單計算)
        lgpl_low = value_len // 2 % 256
        lgpl_high = 0  # LGPL版只設置低位
        lgpl_result = f"Low: {lgpl_low}, High: {lgpl_high}"
        
        print(f"  商業版: {commercial_result}")
        print(f"  LGPL版: {lgpl_result}")
        
        # 檢查是否相同
        if value_len <= 510:  # 255 * 2
            if commercial_low == lgpl_low and commercial_high == lgpl_high:
                print("  ✅ 結果相同")
            else:
                print("  ❌ 結果不同")
        else:
            print("  ⚠️ 超出LGPL版處理範圍")
        print()

def demonstrate_issues():
    """演示潛在問題"""
    print("⚠️ 潛在問題演示:")
    print("=" * 30)
    
    # 大數值測試
    large_values = [512, 1024, 2048, 65534]
    
    for value_len in large_values:
        print(f"數據長度: {value_len} 字節 (點數: {value_len//2})")
        
        # 商業版方法
        commercial_packed = struct.pack('<H', value_len//2)
        commercial_low = commercial_packed[0]
        commercial_high = commercial_packed[1]
        
        # LGPL版方法
        lgpl_low = value_len // 2 % 256
        lgpl_high = 0
        
        print(f"  商業版: Low={commercial_low}, High={commercial_high}")
        print(f"  LGPL版: Low={lgpl_low}, High={lgpl_high}")
        
        # 重建數值檢查
        commercial_reconstructed = commercial_low + (commercial_high << 8)
        lgpl_reconstructed = lgpl_low + (lgpl_high << 8)
        
        print(f"  重建數值 - 商業版: {commercial_reconstructed}, LGPL版: {lgpl_reconstructed}")
        
        if commercial_reconstructed != lgpl_reconstructed:
            print("  ❌ 數值不匹配！可能導致通信錯誤")
        else:
            print("  ✅ 數值匹配")
        print()

def recommend_solution():
    """推薦解決方案"""
    print("💡 解決方案建議:")
    print("=" * 30)
    
    print("問題分析:")
    print("1. 商業版使用 struct.pack('<H', value) 正確處理16位無符號整數")
    print("2. LGPL版只處理低8位，忽略高8位")
    print("3. 當數據點數 > 255 時，LGPL版會出現錯誤")
    print()
    
    print("推薦改進:")
    print("將 LGPL 版改為與商業版相同的方法")
    print()
    
    print("改進前 (LGPL版):")
    print("_PLCCommand[10] = len(value) // 2 % 256")
    print("_PLCCommand[11] = 0x00")
    print()
    
    print("改進後 (推薦):")
    print("packed = struct.pack('<H', len(value)//2)")
    print("_PLCCommand[10] = packed[0]")
    print("_PLCCommand[11] = packed[1]")
    print()
    
    print("或者更簡潔的方式:")
    print("point_count = len(value) // 2")
    print("_PLCCommand[10] = point_count % 256")
    print("_PLCCommand[11] = point_count // 256")

def test_protocol_compliance():
    """測試協議合規性"""
    print("\n📋 協議合規性測試:")
    print("=" * 30)
    
    print("三菱A1E協議要求:")
    print("- 軟元件點數為16位無符號整數")
    print("- 低位在前，高位在後 (Little Endian)")
    print("- 最大支援65535個點")
    print()
    
    # 測試邊界值
    boundary_tests = [
        (255, "8位邊界"),
        (256, "超過8位"),
        (65535, "16位最大值")
    ]
    
    for point_count, description in boundary_tests:
        value_len = point_count * 2
        
        print(f"{description} - 點數: {point_count}")
        
        # 商業版方法
        commercial_packed = struct.pack('<H', point_count)
        commercial_low = commercial_packed[0]
        commercial_high = commercial_packed[1]
        
        # LGPL版方法
        lgpl_low = point_count % 256
        lgpl_high = 0
        
        print(f"  商業版: 0x{commercial_high:02X}{commercial_low:02X}")
        print(f"  LGPL版: 0x{lgpl_high:02X}{lgpl_low:02X}")
        
        if commercial_low == lgpl_low and commercial_high == lgpl_high:
            print("  ✅ 協議合規")
        else:
            print("  ❌ 協議不合規")
        print()

if __name__ == "__main__":
    analyze_byte_packing_differences()
    demonstrate_issues()
    recommend_solution()
    test_protocol_compliance()
    
    print("\n🎯 結論:")
    print("商業版的 struct.pack 方法更好，因為:")
    print("1. ✅ 正確處理16位數值")
    print("2. ✅ 符合協議規範") 
    print("3. ✅ 支援大數據量")
    print("4. ✅ 避免數據截斷")
    print("\n建議將 LGPL 版改為商業版的實現方式。")
