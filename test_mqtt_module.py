#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試MQTT模組功能
"""

import sys
import os

# 添加當前目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mqtt_classes():
    """測試MQTT類的基本功能"""
    print("🔧 測試MQTT類基本功能")
    print("=" * 50)
    
    try:
        from mqtt import (
            MqttQualityOfServiceLevel, MqttControlMessage, OperateResult,
            MqttCredential, MqttConnectionOptions, MqttApplicationMessage,
            MqttHelper, MqttSyncClient
        )
        
        # 測試枚舉類
        print("📋 測試枚舉和常量:")
        print(f"   QoS等級: {MqttQualityOfServiceLevel.AtMostOnce.value}")
        print(f"   控制消息: CONNECT={MqttControlMessage.CONNECT}")
        print("   ✅ 枚舉類正常")
        
        # 測試操作結果類
        print("\n📋 測試操作結果類:")
        success_result = OperateResult.CreateSuccessResult("test_data")
        failed_result = OperateResult.CreateFailedResult("test_error")
        print(f"   成功結果: {success_result.IsSuccess}, 內容: {success_result.Content}")
        print(f"   失敗結果: {failed_result.IsSuccess}, 消息: {failed_result.Message}")
        print("   ✅ 操作結果類正常")
        
        # 測試認證類
        print("\n📋 測試認證類:")
        credential = MqttCredential("testuser", "testpass")
        print(f"   用戶名: {credential.UserName}")
        print(f"   密碼: {credential.Password}")
        print("   ✅ 認證類正常")
        
        # 測試連接選項
        print("\n📋 測試連接選項:")
        options = MqttConnectionOptions()
        options.ClientId = "test_client"
        options.IpAddress = "*************"
        options.Credentials = credential
        print(f"   客戶端ID: {options.ClientId}")
        print(f"   服務器地址: {options.IpAddress}:{options.Port}")
        print("   ✅ 連接選項正常")
        
        # 測試應用消息
        print("\n📋 測試應用消息:")
        app_msg = MqttApplicationMessage()
        app_msg.Topic = "test/topic"
        app_msg.Payload = bytearray("Hello MQTT".encode('utf-8'))
        print(f"   主題: {app_msg.Topic}")
        print(f"   負載長度: {len(app_msg.Payload)} 字節")
        print("   ✅ 應用消息正常")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_mqtt_helper():
    """測試MQTT輔助工具"""
    print("\n🔧 測試MQTT輔助工具")
    print("=" * 50)
    
    try:
        from mqtt import MqttHelper, MqttConnectionOptions, MqttCredential
        
        # 測試長度編碼
        print("📋 測試長度編碼:")
        test_lengths = [10, 127, 128, 16383, 16384]
        for length in test_lengths:
            result = MqttHelper.calculate_length_to_mqtt_length(length)
            if result.IsSuccess:
                encoded = result.Content
                print(f"   長度 {length}: {[hex(b) for b in encoded]}")
            else:
                print(f"   長度 {length}: 編碼失敗")
        print("   ✅ 長度編碼正常")
        
        # 測試字符串分段命令
        print("\n📋 測試字符串分段:")
        test_strings = ["", "hello", "測試中文", "a" * 300]
        for test_str in test_strings:
            seg_cmd = MqttHelper.build_seg_command_by_string(test_str)
            length = seg_cmd[0] * 256 + seg_cmd[1]
            print(f"   字符串 '{test_str[:10]}...': 長度={length}, 總字節={len(seg_cmd)}")
        print("   ✅ 字符串分段正常")
        
        # 測試連接命令構建
        print("\n📋 測試連接命令構建:")
        options = MqttConnectionOptions()
        options.ClientId = "test_client_123"
        options.Credentials = MqttCredential("user", "pass")
        
        connect_cmd = MqttHelper.build_connect_mqtt_command(options)
        if connect_cmd.IsSuccess:
            cmd_bytes = connect_cmd.Content
            print(f"   連接命令長度: {len(cmd_bytes)} 字節")
            print(f"   命令頭: {hex(cmd_bytes[0])}")
            print("   ✅ 連接命令構建正常")
        else:
            print(f"   ❌ 連接命令構建失敗: {connect_cmd.Message}")
        
        # 測試發布命令構建
        print("\n📋 測試發布命令構建:")
        topic = "test/data"
        payload = bytearray("Hello World".encode('utf-8'))
        
        publish_cmd = MqttHelper.build_publish_mqtt_command(topic, payload)
        if publish_cmd.IsSuccess:
            cmd_bytes = publish_cmd.Content
            print(f"   發布命令長度: {len(cmd_bytes)} 字節")
            print(f"   命令頭: {hex(cmd_bytes[0])}")
            print("   ✅ 發布命令構建正常")
        else:
            print(f"   ❌ 發布命令構建失敗: {publish_cmd.Message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 輔助工具測試失敗: {e}")
        return False

def test_mqtt_client_creation():
    """測試MQTT客戶端創建"""
    print("\n🔧 測試MQTT客戶端創建")
    print("=" * 50)
    
    try:
        from mqtt import MqttSyncClient, MqttConnectionOptions, MqttCredential
        
        # 測試簡單創建
        print("📋 測試簡單客戶端創建:")
        client1 = MqttSyncClient("*************", 1883)
        print(f"   服務器: {client1.ip_address}:{client1.port}")
        print(f"   客戶端ID: {client1.connection_options.ClientId}")
        print("   ✅ 簡單創建正常")
        
        # 測試選項創建
        print("\n📋 測試選項客戶端創建:")
        options = MqttConnectionOptions()
        options.ClientId = "advanced_client"
        options.IpAddress = "mqtt.example.com"
        options.Port = 8883
        options.Credentials = MqttCredential("admin", "secret")
        
        client2 = MqttSyncClient(options)
        print(f"   服務器: {client2.ip_address}:{client2.port}")
        print(f"   客戶端ID: {client2.connection_options.ClientId}")
        print(f"   認證: {client2.connection_options.Credentials.UserName}")
        print("   ✅ 選項創建正常")
        
        # 測試客戶端屬性
        print("\n📋 測試客戶端屬性:")
        print(f"   連接狀態: {client1.is_connected}")
        print(f"   接收超時: {client1.receive_timeout}秒")
        print(f"   連接超時: {client1.connect_timeout}秒")
        print("   ✅ 客戶端屬性正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 客戶端創建測試失敗: {e}")
        return False

def generate_test_summary(test_results):
    """生成測試總結"""
    print("\n📋 測試總結")
    print("=" * 50)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results)
    
    print(f"總測試數: {total_tests}")
    print(f"通過測試: {passed_tests}")
    print(f"失敗測試: {total_tests - passed_tests}")
    print(f"通過率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有測試通過！MQTT模組準備就緒")
        print("\n📋 功能特點:")
        print("✅ 完整的MQTT協議支援")
        print("✅ 同步客戶端實現")
        print("✅ 連接選項配置")
        print("✅ 認證支援")
        print("✅ 消息發布和讀取")
        print("✅ 上下文管理器支援")
        
        print("\n💡 使用建議:")
        print("- 適用於同步MQTT通信場景")
        print("- 支援基本的發布/訂閱功能")
        print("- 可用於工業物聯網數據傳輸")
        print("- 建議在生產環境中添加重連機制")
    else:
        print("\n⚠️ 部分測試失敗，請檢查實現")

if __name__ == "__main__":
    print("🚀 開始測試MQTT模組")
    print("=" * 60)
    
    # 運行所有測試
    test_results = []
    
    test_results.append(test_mqtt_classes())
    test_results.append(test_mqtt_helper())
    test_results.append(test_mqtt_client_creation())
    
    # 生成測試總結
    generate_test_summary(test_results)
    
    print(f"\n📄 MQTT模組文件已創建: mqtt.py")
    print(f"📄 配置模板將在運行 mqtt.py 時創建")
    
    print(f"\n🎯 下一步建議:")
    print(f"1. 安裝MQTT服務器（如 Mosquitto）")
    print(f"2. 運行 python mqtt.py 查看使用示例")
    print(f"3. 根據需要修改配置參數")
    print(f"4. 在實際項目中集成使用")
