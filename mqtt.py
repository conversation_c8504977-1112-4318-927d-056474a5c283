#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT客戶端實現 - 基於商業版HslCommunication分析
自用版本，支援基本的MQTT通信功能
"""

import socket
import struct
import threading
import time
from enum import Enum
from typing import Optional, Callable, Tuple


class MqttQualityOfServiceLevel(Enum):
    """MQTT消息質量等級"""
    AtMostOnce = 0      # 最多一次
    AtLeastOnce = 1     # 至少一次  
    ExactlyOnce = 2     # 恰好一次
    OnlyTransfer = 3    # 僅傳輸


class MqttControlMessage:
    """MQTT控制消息類型"""
    FAILED = 0x00
    CONNECT = 0x01
    CONNACK = 0x02
    PUBLISH = 0x03
    PUBACK = 0x04
    PUBREC = 0x05
    PUBREL = 0x06
    PUBCOMP = 0x07
    SUBSCRIBE = 0x08
    SUBACK = 0x09
    UNSUBSCRIBE = 0x0A
    UNSUBACK = 0x0B
    PINGREQ = 0x0C
    PINGRESP = 0x0D
    DISCONNECT = 0x0E
    REPORTPROGRESS = 0x0F


class OperateResult:
    """操作結果類"""
    def __init__(self, success=True, error_code=0, message="", content=None):
        self.IsSuccess = success
        self.ErrorCode = error_code
        self.Message = message
        self.Content = content
        self.Content1 = None
        self.Content2 = None
    
    @staticmethod
    def CreateSuccessResult(content1=None, content2=None):
        result = OperateResult(True, 0, "Success")
        result.Content = content1
        result.Content1 = content1
        result.Content2 = content2
        return result
    
    @staticmethod
    def CreateFailedResult(message, error_code=1):
        return OperateResult(False, error_code, message)


class MqttCredential:
    """MQTT認證信息"""
    def __init__(self, username: str, password: str):
        self.UserName = username
        self.Password = password


class MqttConnectionOptions:
    """MQTT連接選項"""
    def __init__(self):
        self.ClientId = ""
        self.IpAddress = "127.0.0.1"
        self.Port = 1883
        self.KeepAlivePeriod = 60
        self.KeepAliveSendInterval = 30
        self.CleanSession = True
        self.ConnectTimeout = 5000
        self.Credentials: Optional[MqttCredential] = None


class MqttApplicationMessage:
    """MQTT應用消息"""
    def __init__(self):
        self.QualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce
        self.Topic: str = ""
        self.Payload: bytearray = bytearray()
        self.Retain = False
    
    def __str__(self):
        return self.Topic


class MqttPublishMessage:
    """MQTT發布消息"""
    def __init__(self):
        self.IsSendFirstTime = True
        self.Identifier = 0
        self.Message: MqttApplicationMessage = None


class MqttHelper:
    """MQTT輔助工具類"""
    
    @staticmethod
    def calculate_length_to_mqtt_length(length: int) -> OperateResult:
        """計算MQTT長度編碼"""
        if length > 268_435_455:
            return OperateResult.CreateFailedResult("MQTT數據過長")
        
        if length < 128:
            return OperateResult.CreateSuccessResult(bytearray([length]))
        elif length < 128 * 128:
            buffer = bytearray(2)
            buffer[0] = length % 128 + 0x80
            buffer[1] = length // 128
            return OperateResult.CreateSuccessResult(buffer)
        elif length < 128 * 128 * 128:
            buffer = bytearray(3)
            buffer[0] = length % 128 + 0x80
            buffer[1] = length // 128 % 128 + 0x80
            buffer[2] = length // 128 // 128
            return OperateResult.CreateSuccessResult(buffer)
        else:
            buffer = bytearray(4)
            buffer[0] = length % 128 + 0x80
            buffer[1] = length // 128 % 128 + 0x80
            buffer[2] = length // 128 // 128 % 128 + 0x80
            buffer[3] = length // 128 // 128 // 128
            return OperateResult.CreateSuccessResult(buffer)
    
    @staticmethod
    def build_mqtt_command(control: int, flags: int, variable_header: bytearray, payload: bytearray) -> OperateResult:
        """構建MQTT命令"""
        if variable_header is None:
            variable_header = bytearray()
        if payload is None:
            payload = bytearray()
        
        control = control << 4
        head = control | flags
        
        # 計算長度信息
        buffer_length = MqttHelper.calculate_length_to_mqtt_length(len(variable_header) + len(payload))
        if not buffer_length.IsSuccess:
            return buffer_length
        
        ms = bytearray()
        ms.append(head)
        ms.extend(buffer_length.Content)
        if len(variable_header) > 0:
            ms.extend(variable_header)
        if len(payload) > 0:
            ms.extend(payload)
        
        return OperateResult.CreateSuccessResult(ms)
    
    @staticmethod
    def build_seg_command_by_string(message: str) -> bytearray:
        """將字符串打包成UTF8編碼，帶長度信息"""
        buffer = bytearray(2)
        if message and len(message) > 0:
            buffer.extend(message.encode("utf-8"))
        
        length = len(buffer) - 2
        buffer[0] = length // 256
        buffer[1] = length % 256
        return buffer
    
    @staticmethod
    def build_int_bytes(data: int) -> bytearray:
        """構建整數字節"""
        temp = struct.pack('>H', data)  # 大端序
        return bytearray(temp)
    
    @staticmethod
    def build_connect_mqtt_command(connection_options: MqttConnectionOptions, protocol: str = "MQTT") -> OperateResult:
        """創建MQTT連接命令"""
        variable_header = bytearray()
        variable_header.extend(bytearray([0x00, 0x04]))
        variable_header.extend(protocol.encode('ascii'))
        variable_header.append(0x04)  # 協議版本
        
        connect_flags = 0x00
        if connection_options.Credentials is not None:
            connect_flags |= 0x80  # 用戶名標誌
            connect_flags |= 0x40  # 密碼標誌
        if connection_options.CleanSession:
            connect_flags |= 0x02  # 清理會話標誌
        
        variable_header.append(connect_flags)
        
        if connection_options.KeepAlivePeriod < 1:
            connection_options.KeepAlivePeriod = 1
        variable_header.extend(MqttHelper.build_int_bytes(connection_options.KeepAlivePeriod))
        
        payload = bytearray()
        payload.extend(MqttHelper.build_seg_command_by_string(connection_options.ClientId))
        
        if connection_options.Credentials is not None:
            payload.extend(MqttHelper.build_seg_command_by_string(connection_options.Credentials.UserName))
            payload.extend(MqttHelper.build_seg_command_by_string(connection_options.Credentials.Password))
        
        return MqttHelper.build_mqtt_command(MqttControlMessage.CONNECT, 0x00, variable_header, payload)
    
    @staticmethod
    def build_publish_mqtt_command(topic: str, payload: bytearray) -> OperateResult:
        """創建MQTT發布命令"""
        return MqttHelper.build_mqtt_command(
            MqttControlMessage.PUBLISH, 
            0x00, 
            MqttHelper.build_seg_command_by_string(topic), 
            payload
        )
    
    @staticmethod
    def check_connect_back(code: int, data: bytearray) -> OperateResult:
        """檢查連接回應"""
        if code >> 4 != MqttControlMessage.CONNACK:
            return OperateResult.CreateFailedResult(f"MQTT連接回應錯誤: {code}")
        
        if len(data) < 2:
            return OperateResult.CreateFailedResult("MQTT連接數據過短")
        
        status = data[1]  # 連接返回碼
        if status > 0:
            return OperateResult.CreateFailedResult(f"MQTT連接失敗，狀態碼: {status}")
        
        return OperateResult.CreateSuccessResult()
    
    @staticmethod
    def extra_mqtt_receive_data(mqtt_code: int, data: bytearray) -> OperateResult:
        """解析MQTT接收數據"""
        if len(data) < 2:
            return OperateResult.CreateFailedResult("接收數據長度過短")
        
        topic_length = data[0] * 256 + data[1]
        if len(data) < (2 + topic_length):
            return OperateResult.CreateFailedResult("主題長度錯誤")
        
        topic = ""
        if topic_length > 0:
            topic = data[2:2+topic_length].decode('utf-8')
        
        payload = data[2+topic_length:]
        return OperateResult.CreateSuccessResult(topic, payload)


class MqttSyncClient:
    """MQTT同步客戶端"""

    def __init__(self, ip_address: str, port: int = 1883):
        """初始化MQTT客戶端"""
        if isinstance(ip_address, MqttConnectionOptions):
            self.connection_options = ip_address
            self.ip_address = ip_address.IpAddress
            self.port = ip_address.Port
        else:
            self.connection_options = MqttConnectionOptions()
            self.connection_options.IpAddress = ip_address
            self.connection_options.Port = port
            self.ip_address = ip_address
            self.port = port

        self.socket: Optional[socket.socket] = None
        self.is_connected = False
        self.receive_timeout = 60
        self.connect_timeout = 5
        self.lock = threading.Lock()
        self.increment_count = 1

    def connect(self) -> OperateResult:
        """連接到MQTT服務器"""
        try:
            # 創建socket連接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.connect_timeout)
            self.socket.connect((self.ip_address, self.port))

            # 發送連接命令
            command = MqttHelper.build_connect_mqtt_command(self.connection_options)
            if not command.IsSuccess:
                return command

            self.socket.send(command.Content)

            # 接收連接回應
            response = self._receive_mqtt_message()
            if not response.IsSuccess:
                return response

            # 檢查連接回應
            check = MqttHelper.check_connect_back(response.Content1, response.Content2)
            if not check.IsSuccess:
                self.disconnect()
                return check

            self.is_connected = True
            return OperateResult.CreateSuccessResult()

        except Exception as e:
            return OperateResult.CreateFailedResult(f"連接失敗: {str(e)}")

    def disconnect(self):
        """斷開MQTT連接"""
        if self.socket:
            try:
                # 發送斷開連接命令
                disconnect_cmd = MqttHelper.build_mqtt_command(MqttControlMessage.DISCONNECT, 0x00, bytearray(), bytearray())
                if disconnect_cmd.IsSuccess:
                    self.socket.send(disconnect_cmd.Content)
            except:
                pass
            finally:
                self.socket.close()
                self.socket = None
                self.is_connected = False

    def publish(self, topic: str, payload: bytearray, qos: MqttQualityOfServiceLevel = MqttQualityOfServiceLevel.AtMostOnce) -> OperateResult:
        """發布消息到指定主題"""
        if not self.is_connected:
            return OperateResult.CreateFailedResult("未連接到MQTT服務器")

        try:
            command = MqttHelper.build_publish_mqtt_command(topic, payload)
            if not command.IsSuccess:
                return command

            self.socket.send(command.Content)
            return OperateResult.CreateSuccessResult()

        except Exception as e:
            return OperateResult.CreateFailedResult(f"發布失敗: {str(e)}")

    def publish_string(self, topic: str, message: str) -> OperateResult:
        """發布字符串消息"""
        payload = bytearray(message.encode('utf-8'))
        return self.publish(topic, payload)

    def read(self, topic: str, payload: bytearray) -> OperateResult:
        """從MQTT服務器讀取數據"""
        if not self.is_connected:
            return OperateResult.CreateFailedResult("未連接到MQTT服務器")

        try:
            # 發送請求
            command = MqttHelper.build_publish_mqtt_command(topic, payload)
            if not command.IsSuccess:
                return command

            self.socket.send(command.Content)

            # 接收回應
            response = self._receive_mqtt_message()
            if not response.IsSuccess:
                return response

            return MqttHelper.extra_mqtt_receive_data(MqttControlMessage.PUBLISH, response.Content2)

        except Exception as e:
            return OperateResult.CreateFailedResult(f"讀取失敗: {str(e)}")

    def read_string(self, topic: str, payload: str = "") -> OperateResult:
        """讀取字符串數據"""
        payload_bytes = bytearray(payload.encode('utf-8'))
        result = self.read(topic, payload_bytes)

        if result.IsSuccess:
            try:
                content_str = result.Content2.decode('utf-8')
                return OperateResult.CreateSuccessResult(result.Content1, content_str)
            except:
                return OperateResult.CreateSuccessResult(result.Content1, result.Content2)

        return result

    def _receive_mqtt_message(self) -> OperateResult:
        """接收MQTT消息"""
        try:
            # 接收固定頭部
            header = self.socket.recv(1)
            if len(header) != 1:
                return OperateResult.CreateFailedResult("接收頭部失敗")

            # 接收剩餘長度
            remaining_length = 0
            multiplier = 1

            while True:
                byte = self.socket.recv(1)
                if len(byte) != 1:
                    return OperateResult.CreateFailedResult("接收長度失敗")

                remaining_length += (byte[0] & 0x7F) * multiplier
                if (byte[0] & 0x80) == 0:
                    break
                multiplier *= 128
                if multiplier > 128 * 128 * 128:
                    return OperateResult.CreateFailedResult("長度編碼錯誤")

            # 接收剩餘數據
            data = bytearray()
            if remaining_length > 0:
                data = bytearray(self.socket.recv(remaining_length))
                if len(data) != remaining_length:
                    return OperateResult.CreateFailedResult("接收數據不完整")

            return OperateResult.CreateSuccessResult(header[0], data)

        except Exception as e:
            return OperateResult.CreateFailedResult(f"接收消息失敗: {str(e)}")

    def __enter__(self):
        """上下文管理器入口"""
        result = self.connect()
        if not result.IsSuccess:
            raise Exception(result.Message)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


# 使用示例和測試代碼
def test_mqtt_client():
    """測試MQTT客戶端功能"""
    print("🚀 開始測試MQTT客戶端")
    print("=" * 50)

    # 創建連接選項
    options = MqttConnectionOptions()
    options.ClientId = "test_client_001"
    options.IpAddress = "127.0.0.1"  # 本地MQTT服務器
    options.Port = 1883
    options.KeepAlivePeriod = 60
    options.CleanSession = True

    # 如果需要認證，取消註釋以下行
    # options.Credentials = MqttCredential("username", "password")

    # 測試基本連接
    print("📡 測試MQTT連接...")
    client = MqttSyncClient(options)

    try:
        # 連接測試
        result = client.connect()
        if result.IsSuccess:
            print("✅ MQTT連接成功")

            # 發布測試
            print("\n📤 測試消息發布...")
            publish_result = client.publish_string("test/topic", "Hello MQTT!")
            if publish_result.IsSuccess:
                print("✅ 消息發布成功")
            else:
                print(f"❌ 消息發布失敗: {publish_result.Message}")

            # 讀取測試（如果服務器支持）
            print("\n📥 測試數據讀取...")
            read_result = client.read_string("test/read", "request_data")
            if read_result.IsSuccess:
                print(f"✅ 數據讀取成功: {read_result.Content2}")
            else:
                print(f"❌ 數據讀取失敗: {read_result.Message}")

        else:
            print(f"❌ MQTT連接失敗: {result.Message}")

    except Exception as e:
        print(f"❌ 測試過程中發生異常: {e}")

    finally:
        client.disconnect()
        print("\n🔌 MQTT連接已斷開")


def example_usage():
    """使用示例"""
    print("\n📋 MQTT客戶端使用示例:")
    print("=" * 30)

    example_code = '''
# 基本使用示例
from mqtt import MqttSyncClient, MqttConnectionOptions, MqttCredential

# 方法1: 簡單連接
client = MqttSyncClient("*************", 1883)

# 方法2: 使用連接選項
options = MqttConnectionOptions()
options.ClientId = "my_client"
options.IpAddress = "*************"
options.Port = 1883
options.Credentials = MqttCredential("user", "pass")
client = MqttSyncClient(options)

# 連接並使用
result = client.connect()
if result.IsSuccess:
    # 發布消息
    client.publish_string("sensors/temperature", "25.6")

    # 讀取數據
    response = client.read_string("api/get_data", "request")
    if response.IsSuccess:
        print(f"收到數據: {response.Content2}")

    client.disconnect()

# 使用上下文管理器（推薦）
with MqttSyncClient("*************", 1883) as client:
    client.publish_string("test/message", "Hello World!")
    '''

    print(example_code)


def create_mqtt_config_template():
    """創建MQTT配置模板"""
    config_template = '''
# MQTT配置示例
MQTT_CONFIG = {
    "broker": {
        "host": "127.0.0.1",
        "port": 1883,
        "keepalive": 60
    },
    "client": {
        "client_id": "python_mqtt_client",
        "clean_session": True,
        "username": None,  # 如需認證請填寫
        "password": None   # 如需認證請填寫
    },
    "topics": {
        "publish": "device/data",
        "subscribe": "device/command"
    }
}
'''

    with open("mqtt_config_template.py", "w", encoding="utf-8") as f:
        f.write(config_template)

    print("📄 已創建MQTT配置模板: mqtt_config_template.py")


if __name__ == "__main__":
    print("🔧 MQTT客戶端模組")
    print("基於商業版HslCommunication分析實現")
    print("=" * 60)

    # 顯示使用示例
    example_usage()

    # 創建配置模板
    create_mqtt_config_template()

    # 運行測試（需要MQTT服務器）
    test_choice = input("\n是否運行MQTT連接測試？(需要本地MQTT服務器) [y/N]: ")
    if test_choice.lower() == 'y':
        test_mqtt_client()
    else:
        print("跳過連接測試。請確保有可用的MQTT服務器後再測試。")

    print("\n🎉 MQTT客戶端模組準備就緒！")
