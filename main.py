import torch
# import tensorrt as trt

# print("TensorRT version:", trt.__version__)
print("PyTorch version:", torch.__version__)
print("CUDA available:", torch.cuda.is_available())
if torch.cuda.is_available():    
	print("CUDA device count:", torch.cuda.device_count())    
	print("CUDA device name:", torch.cuda.get_device_name(0))    
	print("Current device index:", torch.cuda.current_device())    
	print("Device capability:", torch.cuda.get_device_capability(0))
else:    
	print("CUDA is NOT available.")